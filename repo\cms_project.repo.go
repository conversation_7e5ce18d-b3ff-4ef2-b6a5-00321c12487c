package repo

import (
	"gitlab.finema.co/finema/csp/csp-api/models"
	core "gitlab.finema.co/finema/idin-core"
	"gitlab.finema.co/finema/idin-core/repository"
)

type CMSProjectOption func(repository.IRepository[models.CMSProject])

var CMSProject = func(c core.IContext, options ...CMSProjectOption) repository.IRepository[models.CMSProject] {
	r := repository.New[models.CMSProject](c)
	for _, opt := range options {
		opt(r)
	}

	return r
}

func CMSProjectOrderBy(pageOptions *core.PageOptions) CMSProjectOption {
	return func(c repository.IRepository[models.CMSProject]) {
		if len(pageOptions.OrderBy) == 0 {
			c.Order("created_at DESC")
		} else {
			c.Order(pageOptions.OrderBy)
		}
	}
}

func CMSProjectByStatus(statuses []string) CMSProjectOption {
	return func(c repository.IRepository[models.CMSProject]) {
		if len(statuses) > 0 {
			c.Where("status IN ?", statuses)
		}
	}
}

func CMSProjectByType(types []string) CMSProjectOption {
	return func(c repository.IRepository[models.CMSProject]) {
		if len(types) > 0 {
			c.Where("type IN ?", types)
		}
	}
}

func CMSProjectByMinistry(ministryID string) CMSProjectOption {
	return func(c repository.IRepository[models.CMSProject]) {
		if ministryID != "" {
			c.Where("ministry_id = ?", ministryID)
		}
	}
}

func CMSProjectByDepartment(departmentID string) CMSProjectOption {
	return func(c repository.IRepository[models.CMSProject]) {
		if departmentID != "" {
			c.Where("department_id = ?", departmentID)
		}
	}
}

func CMSProjectByDivision(divisionID string) CMSProjectOption {
	return func(c repository.IRepository[models.CMSProject]) {
		if divisionID != "" {
			c.Where("division_id = ?", divisionID)
		}
	}
}

func CMSProjectBySearch(query string) CMSProjectOption {
	return func(c repository.IRepository[models.CMSProject]) {
		if query != "" {
			c.Where("name ILIKE ? OR domain ILIKE ? OR contact_name ILIKE ? OR contact_email ILIKE ?",
				"%"+query+"%", "%"+query+"%", "%"+query+"%", "%"+query+"%")
		}
	}
}

func CMSProjectByDateRange(startDate, endDate string) CMSProjectOption {
	return func(c repository.IRepository[models.CMSProject]) {
		if startDate != "" || endDate != "" {
			// Use subquery to avoid DISTINCT issues with PostgreSQL
			subquery := "cms_projects.id IN (SELECT DISTINCT cms_project_id FROM cms_project_phases WHERE 1=1"
			var args []interface{}

			if startDate != "" {
				subquery += " AND start_date >= ?"
				args = append(args, startDate)
			}
			if endDate != "" {
				subquery += " AND end_date <= ?"
				args = append(args, endDate)
			}
			subquery += ")"

			c.Where(subquery, args...)
		}
	}
}

func CMSProjectByPhaseDateRange(startDate, endDate string) CMSProjectOption {
	return func(c repository.IRepository[models.CMSProject]) {
		if startDate != "" || endDate != "" {
			// Use subquery to filter by current phases with date range
			subquery := "cms_projects.id IN (SELECT DISTINCT cms_project_id FROM cms_project_phases WHERE start_date <= NOW() AND end_date >= NOW()"
			var args []interface{}

			if startDate != "" {
				subquery += " AND start_date >= ?"
				args = append(args, startDate)
			}
			if endDate != "" {
				subquery += " AND end_date <= ?"
				args = append(args, endDate)
			}
			subquery += ")"

			c.Where(subquery, args...)
		}
	}
}

func CMSProjectByPhase(phase string, workPhase string) CMSProjectOption {
	return func(c repository.IRepository[models.CMSProject]) {
		if phase != "" || workPhase != "" {
			// Use subquery to avoid DISTINCT issues with PostgreSQL
			subquery := "cms_projects.id IN (SELECT DISTINCT cms_project_id FROM cms_project_phases WHERE 1=1"
			var args []interface{}

			if phase != "" {
				subquery += " AND phase = ?"
				args = append(args, phase)
			}
			if workPhase != "" {
				subquery += " AND work_phase = ?"
				args = append(args, workPhase)
			}
			subquery += ")"

			c.Where(subquery, args...)
		}
	}
}

func CMSProjectByPhaseID(phaseID string) CMSProjectOption {
	return func(c repository.IRepository[models.CMSProject]) {
		if phaseID != "" {
			c.Where("phase_id = ?", phaseID)
		}
	}
}

func CMSProjectWithRelations() CMSProjectOption {
	return func(c repository.IRepository[models.CMSProject]) {
		c.Preload("Ministry").
			Preload("Department").
			Preload("Division").
			Preload("Phase").
			Preload("CreatedBy").
			Preload("UpdatedBy")
	}
}
